<!--
    R<PERSON>ISTER OPTIONS POLICY
    This policy handles the OPTIONS pre-flight requests for the OAuth client registration endpoint.
    It returns the appropriate CORS headers to allow cross-origin requests.
-->
<policies>
    <inbound>
        <!-- Return a 200 OK response with appropriate CORS headers -->
        <return-response>
            <set-status code="200" reason="OK" />
            <set-header name="Access-Control-Allow-Origin" exists-action="override">
                <value>*</value>
            </set-header>
            <set-header name="Access-Control-Allow-Methods" exists-action="override">
                <value>GET, OPTIONS</value>
            </set-header>
            <set-header name="Access-Control-Allow-Headers" exists-action="override">
                <value>Content-Type, Authorization</value>
            </set-header>
            <set-header name="Access-Control-Max-Age" exists-action="override">
                <value>86400</value>
            </set-header>
            <set-body />
        </return-response>
        <base />
    </inbound>
    <backend>
        <base />
    </backend>
    <outbound>
        <base />
    </outbound>
    <on-error>
        <base />
    </on-error>
</policies>
