<!--
    REGISTER POLICY
    This policy implements the dynamic client registration endpoint for OAuth2 flow.
    
    Flow:
    1. MCP client sends a registration request with redirect URIs
    2. We store the registration information in cache for later verification
    3. We generate and return client credentials with the provided redirect URIs
-->
<policies>
    <inbound>
        <base />
        <!-- STEP 1: Extract client registration data from request -->
        <set-variable name="requestBody" value="@(context.Request.Body.As<JObject>(preserveContent: true))" />
          <!-- STEP 2: Store registration information in cache -->
        <cache-store-value duration="3600" 
            key="DynamicClientRegistration" 
            value="@(context.Variables.GetValueOrDefault<JObject>("requestBody").ToString())" />
        
        <!-- Store the redirect URI -->
        <cache-store-value duration="3600" 
            key="ClientRedirectUri" 
            value="@(context.Variables.GetValueOrDefault<JObject>("requestBody")["redirect_uris"][0].ToString())" />

        <!-- Generate a unique client ID (GUID) -->
        <set-variable name="uniqueClientId" value="@(Guid.NewGuid().ToString())" />
        
        <!-- Store client info by client ID for easy lookup during consent -->
        <cache-store-value duration="3600" 
            key="@($"ClientInfo-{context.Variables.GetValueOrDefault<string>("uniqueClientId")}")" 
            value="@{
                var requestBody = context.Variables.GetValueOrDefault<JObject>("requestBody");
                var clientInfo = new JObject();
                clientInfo["client_name"] = requestBody["client_name"]?.ToString() ?? "Unknown Application";
                clientInfo["client_uri"] = requestBody["client_uri"]?.ToString() ?? "";
                clientInfo["redirect_uris"] = requestBody["redirect_uris"];
                return clientInfo.ToString();
            }" />
        
        <!-- STEP 3: Set response content type -->
        <set-header name="Content-Type" exists-action="override">
            <value>application/json</value>
        </set-header>
        
        <!-- STEP 4: Return client credentials response -->
        <return-response>
            <set-status code="200" reason="OK" />
            <set-header name="access-control-allow-origin" exists-action="override">
                <value>*</value>
            </set-header>
            <set-body template="none">@{
                var requestBody = context.Variables.GetValueOrDefault<JObject>("requestBody");
                
                // Generate timestamps dynamically
                // Current time in seconds since epoch (Unix timestamp)
                long currentTimeSeconds = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                
                // Client ID issued at current time
                long clientIdIssuedAt = currentTimeSeconds;
                
                // Client secret expires in 1 year (31536000 seconds = 365 days)
                long clientSecretExpiresAt = currentTimeSeconds + 31536000;

                // Use the generated client ID from earlier
                string uniqueClientId = context.Variables.GetValueOrDefault<string>("uniqueClientId", Guid.NewGuid().ToString());
                
                return new JObject
                {
                    ["client_id"] = uniqueClientId,
                    ["client_id_issued_at"] = clientIdIssuedAt,
                    ["client_secret_expires_at"] = clientSecretExpiresAt,
                    ["redirect_uris"] = requestBody["redirect_uris"]?.ToObject<JArray>(),
                    ["client_name"] = requestBody["client_name"]?.ToString() ?? "Unknown Application",
                    ["client_uri"] = requestBody["client_uri"]?.ToString() ?? ""
                }.ToString();
            }</set-body>
        </return-response>
    </inbound>
    <backend />
    <outbound>
        <base />
    </outbound>
    <on-error>
        <base />
    </on-error>
</policies>