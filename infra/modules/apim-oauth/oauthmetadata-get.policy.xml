<!--
    OAUTH METADATA POLICY
    This policy implements the OpenID Connect and OAuth 2.0 discovery endpoint (.well-known/oauth-authorization-server).
-->
<policies>
    <inbound>
        <!-- Return the OAuth metadata in JSON format -->
        <return-response>
            <set-status code="200" reason="OK" />
            <set-header name="Content-Type" exists-action="override">
                <value>application/json; charset=utf-8</value>
            </set-header>
            <set-header name="access-control-allow-origin" exists-action="override">
                <value>*</value>
            </set-header>                
            <set-body>
                {
                    "issuer": "{{APIMGatewayURL}}",
                    "service_documentation": "https://microsoft.com/",
                    "authorization_endpoint": "{{APIMGatewayURL}}/authorize",
                    "token_endpoint": "{{APIMGatewayURL}}/token",
                    "revocation_endpoint": "{{APIMGatewayURL}}/revoke",
                    "registration_endpoint": "{{APIMGatewayURL}}/register",
                    "response_types_supported": [
                        "code"
                    ],
                    "code_challenge_methods_supported": [
                        "S256"
                    ],
                    "token_endpoint_auth_methods_supported": [
                        "none"
                    ],
                    "grant_types_supported": [
                        "authorization_code",
                        "refresh_token"
                    ],
                    "revocation_endpoint_auth_methods_supported": [
                        "client_secret_post"
                    ]
                }
            </set-body>
        </return-response>
        <base />
    </inbound>
    <backend>
        <base />
    </backend>
    <outbound>
        <base />
    </outbound>
    <on-error>
        <base />
    </on-error>
</policies>