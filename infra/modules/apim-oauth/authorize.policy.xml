<!--
    AUTHORIZE POLICY
    This policy implements the authorization endpoint for PKCE OAuth2 flow with Entra ID.
    
    Flow:
    1. MCP client calls this endpoint with code_challenge and code_challenge_method
    2. We generate a code verifier and challenge for Entra ID
    3. We generate a confirmation code to return to the MCP client later
    4. We redirect the user to Entra ID login page
    5. After authentication, Entra ID will redirect back to the callback endpoint
-->
<policies>
    <inbound>
        <base />
        <!-- STEP 1: Retrieve PKCE parameters from MCP Client request -->
        <set-variable name="mcpClientCodeChallenge" value="@((string)context.Request.Url.Query.GetValueOrDefault("code_challenge", ""))" />
        <set-variable name="mcpClientCodeChallengeMethod" value="@((string)context.Request.Url.Query.GetValueOrDefault("code_challenge_method", ""))" />

        <!-- STEP 2: Extract client ID and check if consent is required -->
        <set-variable name="clientId" value="@((string)context.Request.Url.Query.GetValueOrDefault("client_id", ""))" />
        <set-variable name="redirect_uri" value="@((string)context.Request.Url.Query.GetValueOrDefault("redirect_uri", ""))" />
        <set-variable name="consentStatus" value="@((string)context.Request.Url.Query.GetValueOrDefault("consent", ""))" />
        <set-variable name="currentState" value="@((string)context.Request.Url.Query.GetValueOrDefault("state", ""))" />
        
        <!-- Check for consent cookie -->
        <set-variable name="consentCookie" value="@{
            string cookieName = $"MCP_CLIENT_CONSENT_{context.Variables.GetValueOrDefault<string>("clientId")}";
            return context.Request.Headers.GetValueOrDefault("Cookie", "")
                .Split(';')
                .Select(c => c.Trim().Split('='))
                .Where(c => c.Length == 2 && c[0] == cookieName)
                .Select(c => c[1])
                .FirstOrDefault() ?? "";
        }" />
        
        <!-- Check if the client has already been consented to via cookie or URL parameter -->
        <choose>
            <when condition="@(
                context.Variables.GetValueOrDefault<string>("consentStatus") == "granted" ||
                context.Variables.GetValueOrDefault<string>("consentCookie") == "granted"
            )">
                <!-- Continue with normal flow - client is authorized -->
            </when>
            <otherwise>
                <!-- Redirect to consent page -->
                <return-response>
                    <set-status code="302" reason="Found" />
                    <set-header name="Location" exists-action="override">
                        <value>@{
                            string basePath = context.Request.OriginalUrl.Scheme + "://" + context.Request.OriginalUrl.Host + (context.Request.OriginalUrl.Port == 80 || context.Request.OriginalUrl.Port == 443 ? "" : ":" + context.Request.OriginalUrl.Port);
                            return $"{basePath}/consent?client_id={context.Variables.GetValueOrDefault<string>("clientId")}&redirect_uri={context.Variables.GetValueOrDefault<string>("redirect_uri")}&state={context.Variables.GetValueOrDefault<string>("currentState")}";
                        }</value>
                    </set-header>
                </return-response>
            </otherwise>
        </choose>

        <!-- STEP 3: Generate PKCE parameters for Entra ID authentication -->
        <!-- Generate a random code verifier for Entra ID -->
        <set-variable name="codeVerifier" value="@((string)Guid.NewGuid().ToString().Replace("-", ""))" />
        <!-- Set the code challenge method for Entra ID -->
        <set-variable name="codeChallengeMethod" value="S256" />
        <!-- Generate a code challenge using SHA-256 for Entra ID -->
        <set-variable name="codeChallenge" value="@{
            using (var sha256 = System.Security.Cryptography.SHA256.Create())
            {
                var bytes = System.Text.Encoding.UTF8.GetBytes((string)context.Variables.GetValueOrDefault("codeVerifier", ""));
                var hash = sha256.ComputeHash(bytes);
                return System.Convert.ToBase64String(hash).TrimEnd('=').Replace('+', '-').Replace('/', '_');
            }
            }" />
        
        <!-- STEP 4: Construct the Entra ID authorization URL -->
        <!-- Base URL for Entra ID authorization endpoint -->
        <set-variable name="baseAuthUrl" value="https://login.microsoftonline.com/{{EntraIDTenantId}}/oauth2/v2.0/authorize?response_type=code" />
        <!-- Add client ID parameter - using EntraIDClientId instead of the request client ID -->
        <set-variable name="clientIdParam" value="@("&client_id={{EntraIDClientId}}")" />
        <!-- Add PKCE parameters -->
        <set-variable name="codeChallengeParam" value="@(string.Concat("&code_challenge=", context.Variables.GetValueOrDefault("codeChallenge", "")))" />
        <set-variable name="codeChallengeMethodParam" value="@(string.Concat("&code_challenge_method=", context.Variables.GetValueOrDefault("codeChallengeMethod", "")))" />
        <!-- Add OAuth callback parameter -->
        <set-variable name="redirectUriParam" value="@(string.Concat("&redirect_uri=", "{{OAuthCallbackUri}}" ))" />        
        <!-- Add required scope parameter -->
        <set-variable name="scopeParam" value="@(string.Concat("&scope={{OAuthScopes}}"))" />
        <!-- Generate Entra State -->
        <set-variable name="entraState" value="@((string)Guid.NewGuid().ToString())" />
        <!-- Add state parameter for security -->
        <set-variable name="stateParam" value="@(string.Concat("&state=", context.Variables.GetValueOrDefault("entraState", "")))" />
          <!-- Combine all parts to form the complete authorization URL with PKCE params -->
        <set-variable name="authUrl" value="@(string.Concat(
            context.Variables.GetValueOrDefault("baseAuthUrl", ""), 
            context.Variables.GetValueOrDefault("clientIdParam", ""), 
            context.Variables.GetValueOrDefault("codeChallengeParam", ""), 
            context.Variables.GetValueOrDefault("codeChallengeMethodParam", ""), 
            context.Variables.GetValueOrDefault("redirectUriParam", ""), 
            context.Variables.GetValueOrDefault("scopeParam", ""), 
            context.Variables.GetValueOrDefault("stateParam", "")))" />        
        <!-- STEP 5: Store authentication data in cache for use in callback -->
        <!-- Generate a confirmation code to return to the MCP client -->
        <set-variable name="mcpConfirmConsentCode" value="@((string)Guid.NewGuid().ToString())" />
        
        <!-- Store code verifier for token exchange -->
        <cache-store-value duration="3600" 
            key="@("CodeVerifier-"+context.Variables.GetValueOrDefault("entraState", ""))" 
            value="@(context.Variables.GetValueOrDefault("codeVerifier", ""))" />
        
        <!-- Retrieve MCP Client State and Scope from request (Claude sends the state and scope param for binding the auth flow per request) -->
        <set-variable name="mcpState" value="@((string)context.Request.Url.Query.GetValueOrDefault("state", ""))" />
        <set-variable name="mcpScope" value="@((string)context.Request.Url.Query.GetValueOrDefault("scope", ""))" />

        <!-- Map state to MCP confirmation code for callback -->
        <cache-store-value duration="3600" 
            key="@((string)context.Variables.GetValueOrDefault("entraState"))" 
            value="@(context.Variables.GetValueOrDefault("mcpConfirmConsentCode", ""))" />
        
        <!-- Store MCP client PKCE data, client state and client callback redirect uri for verification during token exchange -->
        <cache-store-value duration="3600" 
            key="@($"McpClientAuthData-{context.Variables.GetValueOrDefault("mcpConfirmConsentCode")}")" 
            value="@{
                // Create a JObject and populate it with values
                return new JObject{
                    ["mcpClientCodeChallenge"] = (string)context.Variables.GetValueOrDefault("mcpClientCodeChallenge", ""),
                    ["mcpClientCodeChallengeMethod"] = (string)context.Variables.GetValueOrDefault("mcpClientCodeChallengeMethod", ""),
                    ["mcpClientState"] = (string)context.Variables.GetValueOrDefault("mcpState", ""),
                    ["mcpClientScope"] = (string)context.Variables.GetValueOrDefault("mcpScope", ""),
                    ["mcpCallbackRedirectUri"] = Uri.UnescapeDataString(context.Variables.GetValueOrDefault("redirect_uri", ""))
                }.ToString();
            }" />
    </inbound>
    <backend>
        <base />
    </backend>
    <outbound>
        <base />        
        <!-- Return the response with a 302 status code for redirect -->
        <return-response>
            <set-status code="302" reason="Found" />
            <set-header name="Location" exists-action="override">
                <value>@(context.Variables.GetValueOrDefault("authUrl", ""))</value>
            </set-header>
        </return-response>
    </outbound>
    <on-error>
        <base />
    </on-error>
</policies>