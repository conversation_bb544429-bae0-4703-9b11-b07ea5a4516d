<policies>
    <inbound>
        <base />
        <validate-jwt header-name="Authorization" failed-validation-httpcode="403" output-token-variable-name="jwt">
			<openid-config url="https://login.microsoftonline.com/{{EntraIDTenantId}}/.well-known/openid-configuration" />
            <audiences>
                <audience>{{ApiAudience}}</audience>
            </audiences>
		</validate-jwt>
    </inbound>
    <backend>
        <base />
    </backend>
    <outbound>
        <base />
    </outbound>
    <on-error>
        <base />
    </on-error>
</policies>
