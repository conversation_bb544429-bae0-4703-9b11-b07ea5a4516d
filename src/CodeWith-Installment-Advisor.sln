Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = ********
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "InstallmentAdvisor.DataApi", "InstallmentAdvisorApi\InstallmentAdvisor.DataApi.csproj", "{085D71A8-1ADA-8F91-F6E5-6D3D7F08089A}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "InstallmentAdvisor.AppHost", "InstallmentAdvisor.AppHost\InstallmentAdvisor.AppHost.csproj", "{9EAC75CA-CDEF-4915-9286-40F05D646267}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "InstallmentAdvisor.ServiceDefaults", "InstallmentAdvisor.ServiceDefaults\InstallmentAdvisor.ServiceDefaults.csproj", "{B945D6E4-1E2C-5F34-0770-43A33C43DE0A}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{A530B71F-F2C1-4862-B3DA-B9D3D8943BFA}"
	ProjectSection(SolutionItems) = preProject
		.editorconfig = .editorconfig
		QuickApiTest.http = QuickApiTest.http
	EndProjectSection
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "InstallmentAdvisor.ChatApi", "ChatApi\InstallmentAdvisor.ChatApi.csproj", "{008E77BB-9A0C-85AC-0872-1CF4966D2BE6}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "InstallmentAdvisor.FoundryAgentProvisioner", "InstallmentAdvisor.FoundryAgentProvisioner\InstallmentAdvisor.FoundryAgentProvisioner.csproj", "{0D2E87B7-65B1-434D-A83F-75BDB3F12FBF}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "InstallmentAdvisor.Settings", "InstallmentAdvisor.Settings\InstallmentAdvisor.Settings.csproj", "{3479367D-**************-50DEA55D7A1D}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{085D71A8-1ADA-8F91-F6E5-6D3D7F08089A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{085D71A8-1ADA-8F91-F6E5-6D3D7F08089A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{085D71A8-1ADA-8F91-F6E5-6D3D7F08089A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{085D71A8-1ADA-8F91-F6E5-6D3D7F08089A}.Release|Any CPU.Build.0 = Release|Any CPU
		{9EAC75CA-CDEF-4915-9286-40F05D646267}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9EAC75CA-CDEF-4915-9286-40F05D646267}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9EAC75CA-CDEF-4915-9286-40F05D646267}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9EAC75CA-CDEF-4915-9286-40F05D646267}.Release|Any CPU.Build.0 = Release|Any CPU
		{B945D6E4-1E2C-5F34-0770-43A33C43DE0A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B945D6E4-1E2C-5F34-0770-43A33C43DE0A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B945D6E4-1E2C-5F34-0770-43A33C43DE0A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B945D6E4-1E2C-5F34-0770-43A33C43DE0A}.Release|Any CPU.Build.0 = Release|Any CPU
		{008E77BB-9A0C-85AC-0872-1CF4966D2BE6}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{008E77BB-9A0C-85AC-0872-1CF4966D2BE6}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{008E77BB-9A0C-85AC-0872-1CF4966D2BE6}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{008E77BB-9A0C-85AC-0872-1CF4966D2BE6}.Release|Any CPU.Build.0 = Release|Any CPU
		{0D2E87B7-65B1-434D-A83F-75BDB3F12FBF}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0D2E87B7-65B1-434D-A83F-75BDB3F12FBF}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0D2E87B7-65B1-434D-A83F-75BDB3F12FBF}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0D2E87B7-65B1-434D-A83F-75BDB3F12FBF}.Release|Any CPU.Build.0 = Release|Any CPU
		{3479367D-**************-50DEA55D7A1D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3479367D-**************-50DEA55D7A1D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3479367D-**************-50DEA55D7A1D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3479367D-**************-50DEA55D7A1D}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {536AFA09-894D-4F10-AC3C-0C9803E31C9A}
	EndGlobalSection
EndGlobal
