﻿<Project Sdk="Microsoft.NET.Sdk">

  <Sdk Name="Aspire.AppHost.Sdk" Version="9.0.0" />

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <IsAspireHost>true</IsAspireHost>
    <UserSecretsId>194aef8f-b3d7-424d-aae6-e7f2250a2b2e</UserSecretsId>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Aspire.Hosting.AppHost" Version="9.3.1" />
    <PackageReference Include="Aspire.Hosting.NodeJs" Version="9.3.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\ChatApi\InstallmentAdvisor.ChatApi.csproj" />
    <ProjectReference Include="..\InstallmentAdvisor.FoundryAgentProvisioner\InstallmentAdvisor.FoundryAgentProvisioner.csproj" />
    <ProjectReference Include="..\InstallmentAdvisorApi\InstallmentAdvisor.DataApi.csproj" />
  </ItemGroup>

</Project>
